using Delivery.Contracts.Models;

namespace Delivery.Contracts.Events;

/// <summary>
/// Domain events for the delivery platform message broker
/// </summary>
public abstract class DomainEvent
{
    public Guid EventId { get; set; } = Guid.NewGuid();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string EventType { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
}

public class OrderCreatedEvent : DomainEvent
{
    public Guid OrderId { get; set; }
    public Guid StoreOwnerId { get; set; }
    public Address PickupAddress { get; set; } = new();
    public Address DropoffAddress { get; set; } = new();
    public PackageDetails PackageDetails { get; set; } = new();
    public decimal DeliveryFee { get; set; }
    public DeliveryPriority Priority { get; set; }

    public OrderCreatedEvent()
    {
        EventType = nameof(OrderCreatedEvent);
    }
}

public class OrderAssignedEvent : DomainEvent
{
    public Guid OrderId { get; set; }
    public Guid AgentId { get; set; }
    public Guid StoreOwnerId { get; set; }
    public DateTime AssignedAt { get; set; }

    public OrderAssignedEvent()
    {
        EventType = nameof(OrderAssignedEvent);
    }
}

public class OrderStatusChangedEvent : DomainEvent
{
    public Guid OrderId { get; set; }
    public OrderStatus PreviousStatus { get; set; }
    public OrderStatus NewStatus { get; set; }
    public Guid? AgentId { get; set; }
    public Guid StoreOwnerId { get; set; }
    public string? Notes { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }

    public OrderStatusChangedEvent()
    {
        EventType = nameof(OrderStatusChangedEvent);
    }
}

public class AgentLocationUpdatedEvent : DomainEvent
{
    public Guid AgentId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Heading { get; set; }
    public double? Speed { get; set; }
    public bool IsAvailable { get; set; }
    public List<Guid> ActiveOrderIds { get; set; } = new();

    public AgentLocationUpdatedEvent()
    {
        EventType = nameof(AgentLocationUpdatedEvent);
    }
}

public class GeofenceTriggeredEvent : DomainEvent
{
    public Guid AgentId { get; set; }
    public Guid OrderId { get; set; }
    public GeofenceEventType EventType { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }

    public GeofenceTriggeredEvent()
    {
        EventType = nameof(GeofenceTriggeredEvent);
    }
}

public class ProofOfDeliverySubmittedEvent : DomainEvent
{
    public Guid OrderId { get; set; }
    public Guid AgentId { get; set; }
    public Guid StoreOwnerId { get; set; }
    public ProofOfDelivery ProofOfDelivery { get; set; } = new();

    public ProofOfDeliverySubmittedEvent()
    {
        EventType = nameof(ProofOfDeliverySubmittedEvent);
    }
}

public class PaymentProcessedEvent : DomainEvent
{
    public Guid OrderId { get; set; }
    public Guid StoreOwnerId { get; set; }
    public Guid? AgentId { get; set; }
    public decimal Amount { get; set; }
    public decimal AgentEarnings { get; set; }
    public decimal PlatformFee { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;

    public PaymentProcessedEvent()
    {
        EventType = nameof(PaymentProcessedEvent);
    }
}

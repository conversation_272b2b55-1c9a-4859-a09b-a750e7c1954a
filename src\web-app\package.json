{"name": "delivery-platform-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "axios": "^1.6.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.0", "@mui/x-date-pickers": "^6.18.0", "dayjs": "^1.11.0", "react-hook-form": "^7.48.0", "@hookform/resolvers": "^3.3.0", "yup": "^1.4.0", "react-query": "^3.39.0", "socket.io-client": "^4.7.0", "leaflet": "^1.9.0", "react-leaflet": "^4.2.0", "@types/leaflet": "^1.9.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}
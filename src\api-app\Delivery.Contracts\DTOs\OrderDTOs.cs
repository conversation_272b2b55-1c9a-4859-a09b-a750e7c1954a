using Delivery.Contracts.Models;

namespace Delivery.Contracts.DTOs;

/// <summary>
/// Data Transfer Objects for Order operations
/// </summary>
public class CreateOrderRequest
{
    public Address PickupAddress { get; set; } = new();
    public Address DropoffAddress { get; set; } = new();
    public RecipientDetails RecipientDetails { get; set; } = new();
    public PackageDetails PackageDetails { get; set; } = new();
    public string? SpecialInstructions { get; set; }
    public DeliveryPriority Priority { get; set; } = DeliveryPriority.Standard;
}

public class CreateOrderResponse
{
    public Guid OrderId { get; set; }
    public OrderStatus Status { get; set; }
    public decimal DeliveryFee { get; set; }
    public DateTime EstimatedDeliveryTime { get; set; }
    public string Message { get; set; } = string.Empty;
}

public class OrderDetailsResponse
{
    public Guid OrderId { get; set; }
    public Guid StoreOwnerId { get; set; }
    public Guid? AgentId { get; set; }
    public OrderStatus Status { get; set; }
    public Address PickupAddress { get; set; } = new();
    public Address DropoffAddress { get; set; } = new();
    public RecipientDetails RecipientDetails { get; set; } = new();
    public PackageDetails PackageDetails { get; set; } = new();
    public decimal DeliveryFee { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? EstimatedDeliveryTime { get; set; }
    public string? SpecialInstructions { get; set; }
    public AgentDetailsResponse? AssignedAgent { get; set; }
    public ProofOfDelivery? ProofOfDelivery { get; set; }
    public List<OrderStatusUpdate> StatusHistory { get; set; } = new();
}

public class UpdateOrderStatusRequest
{
    public OrderStatus Status { get; set; }
    public string? Notes { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
}

public class OrderStatusUpdate
{
    public OrderStatus Status { get; set; }
    public DateTime Timestamp { get; set; }
    public string? Notes { get; set; }
    public Guid? UpdatedBy { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
}

public class OrderListResponse
{
    public List<OrderSummary> Orders { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
}

public class OrderSummary
{
    public Guid OrderId { get; set; }
    public OrderStatus Status { get; set; }
    public string RecipientName { get; set; } = string.Empty;
    public string DropoffAddress { get; set; } = string.Empty;
    public decimal DeliveryFee { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? EstimatedDeliveryTime { get; set; }
    public string? AgentName { get; set; }
}

public class ProofOfDeliveryRequest
{
    public string? PhotoBase64 { get; set; }
    public string? SignatureBase64 { get; set; }
    public string? Notes { get; set; }
    public double DeliveryLatitude { get; set; }
    public double DeliveryLongitude { get; set; }
}

{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=DeliveryPlatform_Orders;Uid=root;Pwd=password;"}, "Jwt": {"Key": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "DeliveryPlatform", "Audience": "DeliveryPlatform", "ExpiryMinutes": 60}, "Kafka": {"BootstrapServers": "localhost:9092", "GroupId": "order-service"}}
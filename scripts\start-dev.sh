#!/bin/bash

# Delivery Platform Development Startup Script
# This script starts all services for local development

echo "🚀 Starting Delivery Platform Development Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .NET is installed
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET SDK is not installed. Please install .NET 9 SDK."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+."
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Start infrastructure services
echo "🐳 Starting infrastructure services (MySQL, Redis, Kafka)..."
docker-compose up -d mysql redis zookeeper kafka

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check if MySQL is ready
echo "🔍 Checking MySQL connection..."
until docker exec delivery-mysql mysqladmin ping -h"localhost" --silent; do
    echo "⏳ Waiting for MySQL to be ready..."
    sleep 5
done
echo "✅ MySQL is ready!"

# Check if Ka<PERSON><PERSON> is ready
echo "🔍 Checking Kafka connection..."
until docker exec delivery-kafka kafka-topics --bootstrap-server localhost:9092 --list > /dev/null 2>&1; do
    echo "⏳ Waiting for Kafka to be ready..."
    sleep 5
done
echo "✅ Kafka is ready!"

# Create databases
echo "🗄️ Creating databases..."
docker exec delivery-mysql mysql -uroot -ppassword -e "
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Orders;
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Users;
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Location;
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Notifications;
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Realtime;
"

# Start backend services in background
echo "🔧 Starting backend services..."

# Start Order Service
echo "📦 Starting Order Service..."
cd src/api-app/Delivery.OrderService
dotnet run --urls="http://localhost:5001" > ../../../logs/order-service.log 2>&1 &
ORDER_PID=$!
cd ../../..

# Start User Service
echo "👤 Starting User Service..."
cd src/api-app/Delivery.UserService
dotnet run --urls="http://localhost:5002" > ../../../logs/user-service.log 2>&1 &
USER_PID=$!
cd ../../..

# Start Location Service
echo "📍 Starting Location Service..."
cd src/api-app/Delivery.LocationService
dotnet run --urls="http://localhost:5003" > ../../../logs/location-service.log 2>&1 &
LOCATION_PID=$!
cd ../../..

# Start Notification Service
echo "🔔 Starting Notification Service..."
cd src/api-app/Delivery.NotificationService
dotnet run --urls="http://localhost:5004" > ../../../logs/notification-service.log 2>&1 &
NOTIFICATION_PID=$!
cd ../../..

# Start Realtime Service
echo "⚡ Starting Realtime Service..."
cd src/api-app/Delivery.RealtimeService
dotnet run --urls="http://localhost:5005" > ../../../logs/realtime-service.log 2>&1 &
REALTIME_PID=$!
cd ../../..

# Wait for backend services to start
echo "⏳ Waiting for backend services to start..."
sleep 15

# Start web application
echo "🌐 Starting Web Application..."
cd src/web-app
if [ ! -d "node_modules" ]; then
    echo "📦 Installing web app dependencies..."
    npm install
fi
npm run dev > ../../logs/web-app.log 2>&1 &
WEB_PID=$!
cd ../..

# Create logs directory if it doesn't exist
mkdir -p logs

# Save PIDs for cleanup
echo $ORDER_PID > logs/order-service.pid
echo $USER_PID > logs/user-service.pid
echo $LOCATION_PID > logs/location-service.pid
echo $NOTIFICATION_PID > logs/notification-service.pid
echo $REALTIME_PID > logs/realtime-service.pid
echo $WEB_PID > logs/web-app.pid

echo ""
echo "🎉 Delivery Platform is starting up!"
echo ""
echo "📊 Service Status:"
echo "   🐳 Infrastructure: http://localhost (Docker services)"
echo "   📦 Order Service: http://localhost:5001"
echo "   👤 User Service: http://localhost:5002"
echo "   📍 Location Service: http://localhost:5003"
echo "   🔔 Notification Service: http://localhost:5004"
echo "   ⚡ Realtime Service: http://localhost:5005"
echo "   🌐 Web Application: http://localhost:3000"
echo ""
echo "📋 API Documentation:"
echo "   📖 Order Service Swagger: http://localhost:5001/swagger"
echo "   📖 User Service Swagger: http://localhost:5002/swagger"
echo ""
echo "📝 Logs are available in the 'logs' directory"
echo "🛑 To stop all services, run: ./scripts/stop-dev.sh"
echo ""
echo "✨ THE GLORY is now running! ✨"

version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: delivery-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: DeliveryPlatform
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - delivery-network

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: delivery-redis
    ports:
      - "6379:6379"
    networks:
      - delivery-network

  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: delivery-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - delivery-network

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: delivery-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    networks:
      - delivery-network

  # API Gateway
  api-gateway:
    build:
      context: ./src/api-app/Delivery.ApiGateway
      dockerfile: Dockerfile
    container_name: delivery-api-gateway
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=DeliveryPlatform;Uid=root;Pwd=password;
      - Jwt__Key=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - Kafka__BootstrapServers=kafka:9092
    depends_on:
      - mysql
      - redis
      - kafka
    networks:
      - delivery-network

  # Order Service
  order-service:
    build:
      context: ./src/api-app/Delivery.OrderService
      dockerfile: Dockerfile
    container_name: delivery-order-service
    ports:
      - "5001:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=DeliveryPlatform_Orders;Uid=root;Pwd=password;
      - Jwt__Key=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - Kafka__BootstrapServers=kafka:9092
    depends_on:
      - mysql
      - kafka
    networks:
      - delivery-network

  # User Service
  user-service:
    build:
      context: ./src/api-app/Delivery.UserService
      dockerfile: Dockerfile
    container_name: delivery-user-service
    ports:
      - "5002:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=DeliveryPlatform_Users;Uid=root;Pwd=password;
      - Jwt__Key=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - Kafka__BootstrapServers=kafka:9092
    depends_on:
      - mysql
      - kafka
    networks:
      - delivery-network

  # Location Service
  location-service:
    build:
      context: ./src/api-app/Delivery.LocationService
      dockerfile: Dockerfile
    container_name: delivery-location-service
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=DeliveryPlatform_Location;Uid=root;Pwd=password;
      - Jwt__Key=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - Kafka__BootstrapServers=kafka:9092
      - Redis__ConnectionString=redis:6379
    depends_on:
      - mysql
      - redis
      - kafka
    networks:
      - delivery-network

  # Notification Service
  notification-service:
    build:
      context: ./src/api-app/Delivery.NotificationService
      dockerfile: Dockerfile
    container_name: delivery-notification-service
    ports:
      - "5004:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=DeliveryPlatform_Notifications;Uid=root;Pwd=password;
      - Jwt__Key=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - Kafka__BootstrapServers=kafka:9092
    depends_on:
      - mysql
      - kafka
    networks:
      - delivery-network

  # Realtime Service (SignalR)
  realtime-service:
    build:
      context: ./src/api-app/Delivery.RealtimeService
      dockerfile: Dockerfile
    container_name: delivery-realtime-service
    ports:
      - "5005:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=DeliveryPlatform_Realtime;Uid=root;Pwd=password;
      - Jwt__Key=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - Kafka__BootstrapServers=kafka:9092
      - Redis__ConnectionString=redis:6379
    depends_on:
      - mysql
      - redis
      - kafka
    networks:
      - delivery-network

  # Web Application (React)
  web-app:
    build:
      context: ./src/web-app
      dockerfile: Dockerfile
    container_name: delivery-web-app
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:5000/api
    depends_on:
      - api-gateway
    networks:
      - delivery-network

volumes:
  mysql_data:

networks:
  delivery-network:
    driver: bridge

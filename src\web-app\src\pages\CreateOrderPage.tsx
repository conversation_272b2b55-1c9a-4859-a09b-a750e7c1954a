import React, { useState } from 'react'
import {
  Box,
  Paper,
  Typo<PERSON>,
  <PERSON>per,
  Step,
  StepLabel,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
} from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useNavigate } from 'react-router-dom'
import { useMutation } from 'react-query'
import { ordersApi } from '../services/api'

const steps = ['Pickup Details', 'Delivery Details', 'Package Information', 'Review & Submit']

interface OrderFormData {
  // Pickup Address
  pickupStreet: string
  pickupCity: string
  pickupState: string
  pickupPostalCode: string
  pickupCountry: string
  pickupBuildingInfo?: string
  
  // Delivery Address
  dropoffStreet: string
  dropoffCity: string
  dropoffState: string
  dropoffPostalCode: string
  dropoffCountry: string
  dropoffBuildingInfo?: string
  
  // Recipient Details
  recipientName: string
  recipientPhone: string
  recipientEmail?: string
  recipientAlternatePhone?: string
  
  // Package Details
  packageDescription: string
  packageWeight: number
  packageSize: 'Small' | 'Medium' | 'Large' | 'ExtraLarge'
  packageValue?: number
  isFragile: boolean
  requiresSignature: boolean
  
  // Additional
  specialInstructions?: string
  priority: 'Standard' | 'Express' | 'Urgent'
}

const schema = yup.object({
  pickupStreet: yup.string().required('Pickup street is required'),
  pickupCity: yup.string().required('Pickup city is required'),
  pickupState: yup.string().required('Pickup state is required'),
  pickupPostalCode: yup.string().required('Pickup postal code is required'),
  pickupCountry: yup.string().required('Pickup country is required'),
  
  dropoffStreet: yup.string().required('Delivery street is required'),
  dropoffCity: yup.string().required('Delivery city is required'),
  dropoffState: yup.string().required('Delivery state is required'),
  dropoffPostalCode: yup.string().required('Delivery postal code is required'),
  dropoffCountry: yup.string().required('Delivery country is required'),
  
  recipientName: yup.string().required('Recipient name is required'),
  recipientPhone: yup.string().required('Recipient phone is required'),
  
  packageDescription: yup.string().required('Package description is required'),
  packageWeight: yup.number().positive('Weight must be positive').required('Package weight is required'),
  packageSize: yup.string().required('Package size is required'),
  
  priority: yup.string().required('Priority is required'),
})

const CreateOrderPage: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()

  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
    getValues,
  } = useForm<OrderFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      pickupCountry: 'United States',
      dropoffCountry: 'United States',
      packageSize: 'Medium',
      priority: 'Standard',
      isFragile: false,
      requiresSignature: false,
    },
  })

  const createOrderMutation = useMutation(ordersApi.createOrder, {
    onSuccess: (data) => {
      navigate(`/orders/${data.orderId}`)
    },
    onError: (error: any) => {
      setError(error.message || 'Failed to create order')
    },
  })

  const handleNext = async () => {
    const fieldsToValidate = getFieldsForStep(activeStep)
    const isStepValid = await trigger(fieldsToValidate)
    
    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1)
      setError(null)
    }
  }

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1)
    setError(null)
  }

  const getFieldsForStep = (step: number): (keyof OrderFormData)[] => {
    switch (step) {
      case 0:
        return ['pickupStreet', 'pickupCity', 'pickupState', 'pickupPostalCode', 'pickupCountry']
      case 1:
        return ['dropoffStreet', 'dropoffCity', 'dropoffState', 'dropoffPostalCode', 'dropoffCountry', 'recipientName', 'recipientPhone']
      case 2:
        return ['packageDescription', 'packageWeight', 'packageSize']
      default:
        return []
    }
  }

  const onSubmit = async (data: OrderFormData) => {
    setError(null)
    
    const orderData = {
      pickupAddress: {
        street: data.pickupStreet,
        city: data.pickupCity,
        state: data.pickupState,
        postalCode: data.pickupPostalCode,
        country: data.pickupCountry,
        latitude: 0, // TODO: Geocode address
        longitude: 0,
        buildingInfo: data.pickupBuildingInfo,
      },
      dropoffAddress: {
        street: data.dropoffStreet,
        city: data.dropoffCity,
        state: data.dropoffState,
        postalCode: data.dropoffPostalCode,
        country: data.dropoffCountry,
        latitude: 0, // TODO: Geocode address
        longitude: 0,
        buildingInfo: data.dropoffBuildingInfo,
      },
      recipientDetails: {
        name: data.recipientName,
        phoneNumber: data.recipientPhone,
        email: data.recipientEmail,
        alternatePhoneNumber: data.recipientAlternatePhone,
      },
      packageDetails: {
        description: data.packageDescription,
        weight: data.packageWeight,
        size: data.packageSize,
        value: data.packageValue,
        isFragile: data.isFragile,
        requiresSignature: data.requiresSignature,
      },
      specialInstructions: data.specialInstructions,
      priority: data.priority,
    }

    createOrderMutation.mutate(orderData)
  }

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Pickup Address
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="pickupStreet"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Street Address"
                    error={!!errors.pickupStreet}
                    helperText={errors.pickupStreet?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name="pickupCity"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="City"
                    error={!!errors.pickupCity}
                    helperText={errors.pickupCity?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name="pickupState"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="State/Province"
                    error={!!errors.pickupState}
                    helperText={errors.pickupState?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name="pickupPostalCode"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Postal Code"
                    error={!!errors.pickupPostalCode}
                    helperText={errors.pickupPostalCode?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name="pickupCountry"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Country"
                    error={!!errors.pickupCountry}
                    helperText={errors.pickupCountry?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="pickupBuildingInfo"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Building/Apartment Info (Optional)"
                    multiline
                    rows={2}
                  />
                )}
              />
            </Grid>
          </Grid>
        )
      // Additional cases for other steps would go here...
      default:
        return <Typography>Unknown step</Typography>
    }
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Create New Order
      </Typography>

      <Paper sx={{ p: 3 }}>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit(onSubmit)}>
          {renderStepContent(activeStep)}

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            {activeStep !== 0 && (
              <Button onClick={handleBack} sx={{ mr: 1 }}>
                Back
              </Button>
            )}
            {activeStep === steps.length - 1 ? (
              <Button
                type="submit"
                variant="contained"
                disabled={createOrderMutation.isLoading}
              >
                {createOrderMutation.isLoading ? <CircularProgress size={24} /> : 'Create Order'}
              </Button>
            ) : (
              <Button variant="contained" onClick={handleNext}>
                Next
              </Button>
            )}
          </Box>
        </form>
      </Paper>
    </Box>
  )
}

export default CreateOrderPage

# Use the official .NET runtime as a parent image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the SDK image to build the application
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["Delivery.OrderService/Delivery.OrderService.csproj", "Delivery.OrderService/"]
COPY ["Delivery.Contracts/Delivery.Contracts.csproj", "Delivery.Contracts/"]
RUN dotnet restore "Delivery.OrderService/Delivery.OrderService.csproj"

# Copy everything else and build
COPY . .
WORKDIR "/src/Delivery.OrderService"
RUN dotnet build "Delivery.OrderService.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "Delivery.OrderService.csproj" -c Release -o /app/publish

# Final stage/image
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Delivery.OrderService.dll"]

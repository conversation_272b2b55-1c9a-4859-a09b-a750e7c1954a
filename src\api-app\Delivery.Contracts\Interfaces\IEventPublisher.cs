using Delivery.Contracts.Events;

namespace Delivery.Contracts.Interfaces;

/// <summary>
/// Interface for publishing domain events to message broker
/// </summary>
public interface IEventPublisher
{
    Task PublishAsync<T>(T @event, CancellationToken cancellationToken = default) where T : DomainEvent;
    Task PublishAsync<T>(T @event, string topic, CancellationToken cancellationToken = default) where T : DomainEvent;
    Task PublishBatchAsync<T>(IEnumerable<T> events, CancellationToken cancellationToken = default) where T : DomainEvent;
}

/// <summary>
/// Interface for handling domain events
/// </summary>
public interface IEventHandler<in T> where T : DomainEvent
{
    Task HandleAsync(T @event, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for event subscription management
/// </summary>
public interface IEventSubscriber
{
    Task SubscribeAsync<T>(Func<T, CancellationToken, Task> handler, CancellationToken cancellationToken = default) where T : DomainEvent;
    Task SubscribeAsync<T>(string topic, Func<T, CancellationToken, Task> handler, CancellationToken cancellationToken = default) where T : DomainEvent;
    Task UnsubscribeAsync<T>(CancellationToken cancellationToken = default) where T : DomainEvent;
}

/// <summary>
/// Interface for real-time communication hub
/// </summary>
public interface IRealtimeHub
{
    Task SendToUserAsync(Guid userId, string method, object data, CancellationToken cancellationToken = default);
    Task SendToGroupAsync(string groupName, string method, object data, CancellationToken cancellationToken = default);
    Task AddToGroupAsync(string connectionId, string groupName, CancellationToken cancellationToken = default);
    Task RemoveFromGroupAsync(string connectionId, string groupName, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for notification service
/// </summary>
public interface INotificationService
{
    Task SendPushNotificationAsync(Guid userId, string title, string message, Dictionary<string, object>? data = null, CancellationToken cancellationToken = default);
    Task SendSmsAsync(string phoneNumber, string message, CancellationToken cancellationToken = default);
    Task SendEmailAsync(string email, string subject, string body, CancellationToken cancellationToken = default);
    Task RegisterDeviceTokenAsync(Guid userId, string deviceToken, string platform, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for location and routing service
/// </summary>
public interface ILocationService
{
    Task UpdateAgentLocationAsync(Guid agentId, double latitude, double longitude, CancellationToken cancellationToken = default);
    Task<List<Guid>> FindNearbyAgentsAsync(double latitude, double longitude, double radiusKm, CancellationToken cancellationToken = default);
    Task<Guid?> FindOptimalAgentAsync(Guid orderId, double pickupLatitude, double pickupLongitude, CancellationToken cancellationToken = default);
    Task<double> CalculateDistanceAsync(double lat1, double lon1, double lat2, double lon2, CancellationToken cancellationToken = default);
    Task<int> CalculateEstimatedTimeAsync(double lat1, double lon1, double lat2, double lon2, CancellationToken cancellationToken = default);
}

{"format": 1, "restore": {"F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.UserService\\Delivery.UserService.csproj": {}}, "projects": {"F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.Contracts\\Delivery.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.Contracts\\Delivery.Contracts.csproj", "projectName": "Delivery.Contracts", "projectPath": "F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.Contracts\\Delivery.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.Contracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.UserService\\Delivery.UserService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.UserService\\Delivery.UserService.csproj", "projectName": "Delivery.UserService", "projectPath": "F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.UserService\\Delivery.UserService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.UserService\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.Contracts\\Delivery.Contracts.csproj": {"projectPath": "F:\\0_____sources\\0_____prcsrly\\0_delievery-station\\system-v1\\src\\api-app\\Delivery.Contracts\\Delivery.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.9, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}
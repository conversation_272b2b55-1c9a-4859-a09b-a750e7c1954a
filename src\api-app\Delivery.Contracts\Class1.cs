﻿namespace Delivery.Contracts.Models;

/// <summary>
/// Core domain models for the delivery platform
/// </summary>
public class User
{
    public Guid UserId { get; set; }
    public string Email { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string? PayoutInfo { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}

public class Order
{
    public Guid OrderId { get; set; }
    public Guid StoreOwnerId { get; set; }
    public Guid? AgentId { get; set; }
    public OrderStatus Status { get; set; }
    public Address PickupAddress { get; set; } = new();
    public Address DropoffAddress { get; set; } = new();
    public RecipientDetails RecipientDetails { get; set; } = new();
    public PackageDetails PackageDetails { get; set; } = new();
    public decimal DeliveryFee { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? AssignedAt { get; set; }
    public DateTime? PickedUpAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? EstimatedDeliveryTime { get; set; }
    public string? SpecialInstructions { get; set; }
    public ProofOfDelivery? ProofOfDelivery { get; set; }
}

public class Address
{
    public string Street { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string? BuildingInfo { get; set; }
}

public class RecipientDetails
{
    public string Name { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string? AlternatePhoneNumber { get; set; }
}

public class PackageDetails
{
    public string Description { get; set; } = string.Empty;
    public decimal Weight { get; set; }
    public PackageSize Size { get; set; }
    public decimal? Value { get; set; }
    public bool IsFragile { get; set; }
    public bool RequiresSignature { get; set; }
}

public class ProofOfDelivery
{
    public string? PhotoUrl { get; set; }
    public string? SignatureUrl { get; set; }
    public string? Notes { get; set; }
    public DateTime DeliveredAt { get; set; }
    public double DeliveryLatitude { get; set; }
    public double DeliveryLongitude { get; set; }
}

public class AgentLocation
{
    public Guid AgentId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public DateTime Timestamp { get; set; }
    public bool IsAvailable { get; set; }
    public int CurrentLoad { get; set; }
    public double? Heading { get; set; }
    public double? Speed { get; set; }
}

import React from 'react'
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  But<PERSON>,
  Chip,
} from '@mui/material'
import {
  LocalShipping,
  PendingActions,
  CheckCircle,
  TrendingUp,
  Add,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import { ordersApi } from '../services/api'

const DashboardPage: React.FC = () => {
  const navigate = useNavigate()

  // Fetch recent orders
  const { data: ordersData, isLoading } = useQuery(
    'recent-orders',
    () => ordersApi.getOrders(1, 5),
    {
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  )

  const stats = [
    {
      title: 'Total Orders',
      value: '156',
      icon: <LocalShipping sx={{ fontSize: 40 }} />,
      color: '#1976d2',
      change: '+12%',
    },
    {
      title: 'Pending Orders',
      value: '8',
      icon: <PendingActions sx={{ fontSize: 40 }} />,
      color: '#ed6c02',
      change: '+3',
    },
    {
      title: 'Completed Today',
      value: '23',
      icon: <CheckCircle sx={{ fontSize: 40 }} />,
      color: '#2e7d32',
      change: '+15%',
    },
    {
      title: 'Revenue Today',
      value: '$1,247',
      icon: <TrendingUp sx={{ fontSize: 40 }} />,
      color: '#9c27b0',
      change: '****%',
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'warning'
      case 'assigned':
      case 'accepted':
        return 'info'
      case 'pickedup':
      case 'intransit':
        return 'primary'
      case 'delivered':
        return 'success'
      case 'cancelled':
        return 'error'
      default:
        return 'default'
    }
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => navigate('/orders/new')}
        >
          Create New Order
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      {stat.title}
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" sx={{ color: stat.color }}>
                      {stat.change} from yesterday
                    </Typography>
                  </Box>
                  <Box sx={{ color: stat.color }}>
                    {stat.icon}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Orders */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Recent Orders</Typography>
              <Button onClick={() => navigate('/orders')}>View All</Button>
            </Box>
            
            {isLoading ? (
              <Typography>Loading orders...</Typography>
            ) : (
              <Box>
                {ordersData?.orders?.length > 0 ? (
                  ordersData.orders.map((order: any) => (
                    <Box
                      key={order.orderId}
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        py: 2,
                        borderBottom: '1px solid #eee',
                        '&:last-child': { borderBottom: 'none' },
                      }}
                    >
                      <Box>
                        <Typography variant="subtitle1">
                          Order #{order.orderId.slice(-8)}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {order.recipientName} • {order.dropoffAddress}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {new Date(order.createdAt).toLocaleDateString()}
                        </Typography>
                      </Box>
                      <Box sx={{ textAlign: 'right' }}>
                        <Chip
                          label={order.status}
                          color={getStatusColor(order.status) as any}
                          size="small"
                          sx={{ mb: 1 }}
                        />
                        <Typography variant="subtitle2">
                          ${order.deliveryFee}
                        </Typography>
                      </Box>
                    </Box>
                  ))
                ) : (
                  <Typography color="textSecondary">
                    No orders yet. Create your first order to get started!
                  </Typography>
                )}
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Button
                variant="outlined"
                fullWidth
                startIcon={<Add />}
                onClick={() => navigate('/orders/new')}
              >
                Create New Order
              </Button>
              <Button
                variant="outlined"
                fullWidth
                startIcon={<LocalShipping />}
                onClick={() => navigate('/orders')}
              >
                View All Orders
              </Button>
              <Button
                variant="outlined"
                fullWidth
                startIcon={<TrendingUp />}
                onClick={() => navigate('/analytics')}
              >
                View Analytics
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  )
}

export default DashboardPage

using Microsoft.EntityFrameworkCore;
using Delivery.Contracts.Models;
using Delivery.Contracts.DTOs;
using Delivery.Contracts.Events;
using Delivery.Contracts.Interfaces;
using Delivery.Contracts.Common;
using Delivery.OrderService.Data;

namespace Delivery.OrderService.Services;

/// <summary>
/// Core service for managing delivery orders
/// </summary>
public class OrderManagementService : IOrderManagementService
{
    private readonly OrderDbContext _context;
    private readonly IEventPublisher _eventPublisher;
    private readonly ILogger<OrderManagementService> _logger;

    public OrderManagementService(
        OrderDbContext context,
        IEventPublisher eventPublisher,
        ILogger<OrderManagementService> logger)
    {
        _context = context;
        _eventPublisher = eventPublisher;
        _logger = logger;
    }

    public async Task<ApiResponse<CreateOrderResponse>> CreateOrderAsync(
        Guid storeOwnerId, 
        CreateOrderRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate store owner exists
            var storeOwner = await _context.Users
                .FirstOrDefaultAsync(u => u.UserId == storeOwnerId && u.Role == UserRole.StoreOwner, cancellationToken);
            
            if (storeOwner == null)
            {
                return ApiResponse<CreateOrderResponse>.ErrorResult("Store owner not found");
            }

            // Calculate delivery fee (simplified logic)
            var deliveryFee = CalculateDeliveryFee(request);

            // Create order
            var order = new Order
            {
                OrderId = Guid.NewGuid(),
                StoreOwnerId = storeOwnerId,
                Status = OrderStatus.Pending,
                PickupAddress = request.PickupAddress,
                DropoffAddress = request.DropoffAddress,
                RecipientDetails = request.RecipientDetails,
                PackageDetails = request.PackageDetails,
                DeliveryFee = deliveryFee,
                CreatedAt = DateTime.UtcNow,
                SpecialInstructions = request.SpecialInstructions,
                EstimatedDeliveryTime = CalculateEstimatedDeliveryTime(request.Priority)
            };

            _context.Orders.Add(order);

            // Add status history
            var statusHistory = new OrderStatusHistory
            {
                Id = Guid.NewGuid(),
                OrderId = order.OrderId,
                Status = OrderStatus.Pending,
                Timestamp = DateTime.UtcNow,
                Notes = "Order created",
                UpdatedBy = storeOwnerId
            };

            _context.OrderStatusHistory.Add(statusHistory);

            await _context.SaveChangesAsync(cancellationToken);

            // Publish order created event
            var orderCreatedEvent = new OrderCreatedEvent
            {
                OrderId = order.OrderId,
                StoreOwnerId = storeOwnerId,
                PickupAddress = order.PickupAddress,
                DropoffAddress = order.DropoffAddress,
                PackageDetails = order.PackageDetails,
                DeliveryFee = order.DeliveryFee,
                Priority = request.Priority,
                UserId = storeOwnerId
            };

            await _eventPublisher.PublishAsync(orderCreatedEvent, cancellationToken);

            _logger.LogInformation("Order {OrderId} created successfully for store owner {StoreOwnerId}", 
                order.OrderId, storeOwnerId);

            var response = new CreateOrderResponse
            {
                OrderId = order.OrderId,
                Status = order.Status,
                DeliveryFee = order.DeliveryFee,
                EstimatedDeliveryTime = order.EstimatedDeliveryTime ?? DateTime.UtcNow.AddHours(2),
                Message = "Order created successfully"
            };

            return ApiResponse<CreateOrderResponse>.SuccessResult(response, "Order created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating order for store owner {StoreOwnerId}", storeOwnerId);
            return ApiResponse<CreateOrderResponse>.ErrorResult("Failed to create order");
        }
    }

    public async Task<ApiResponse<OrderDetailsResponse>> GetOrderAsync(
        Guid orderId, 
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var order = await _context.Orders
                .Include(o => o.PickupAddress)
                .Include(o => o.DropoffAddress)
                .Include(o => o.RecipientDetails)
                .Include(o => o.PackageDetails)
                .Include(o => o.ProofOfDelivery)
                .FirstOrDefaultAsync(o => o.OrderId == orderId, cancellationToken);

            if (order == null)
            {
                return ApiResponse<OrderDetailsResponse>.ErrorResult("Order not found");
            }

            // Check authorization
            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId, cancellationToken);
            if (user == null)
            {
                return ApiResponse<OrderDetailsResponse>.ErrorResult("User not found");
            }

            // Authorization check
            if (user.Role == UserRole.StoreOwner && order.StoreOwnerId != userId)
            {
                return ApiResponse<OrderDetailsResponse>.ErrorResult("Unauthorized access");
            }
            
            if (user.Role == UserRole.DeliveryAgent && order.AgentId != userId)
            {
                return ApiResponse<OrderDetailsResponse>.ErrorResult("Unauthorized access");
            }

            // Get status history
            var statusHistory = await _context.OrderStatusHistory
                .Where(h => h.OrderId == orderId)
                .OrderBy(h => h.Timestamp)
                .Select(h => new OrderStatusUpdate
                {
                    Status = h.Status,
                    Timestamp = h.Timestamp,
                    Notes = h.Notes,
                    UpdatedBy = h.UpdatedBy,
                    Latitude = h.Latitude,
                    Longitude = h.Longitude
                })
                .ToListAsync(cancellationToken);

            // Get assigned agent details if available
            AgentDetailsResponse? assignedAgent = null;
            if (order.AgentId.HasValue)
            {
                var agent = await _context.Users
                    .FirstOrDefaultAsync(u => u.UserId == order.AgentId.Value, cancellationToken);
                
                if (agent != null)
                {
                    assignedAgent = new AgentDetailsResponse
                    {
                        UserId = agent.UserId,
                        Email = agent.Email,
                        FirstName = agent.FirstName,
                        LastName = agent.LastName,
                        PhoneNumber = agent.PhoneNumber,
                        Role = agent.Role,
                        CreatedAt = agent.CreatedAt,
                        IsActive = agent.IsActive
                    };
                }
            }

            var response = new OrderDetailsResponse
            {
                OrderId = order.OrderId,
                StoreOwnerId = order.StoreOwnerId,
                AgentId = order.AgentId,
                Status = order.Status,
                PickupAddress = order.PickupAddress,
                DropoffAddress = order.DropoffAddress,
                RecipientDetails = order.RecipientDetails,
                PackageDetails = order.PackageDetails,
                DeliveryFee = order.DeliveryFee,
                CreatedAt = order.CreatedAt,
                EstimatedDeliveryTime = order.EstimatedDeliveryTime,
                SpecialInstructions = order.SpecialInstructions,
                AssignedAgent = assignedAgent,
                ProofOfDelivery = order.ProofOfDelivery,
                StatusHistory = statusHistory
            };

            return ApiResponse<OrderDetailsResponse>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving order {OrderId} for user {UserId}", orderId, userId);
            return ApiResponse<OrderDetailsResponse>.ErrorResult("Failed to retrieve order");
        }
    }

    private decimal CalculateDeliveryFee(CreateOrderRequest request)
    {
        // Simplified delivery fee calculation
        decimal baseFee = 5.00m;
        decimal distanceFee = 0.50m; // per km (simplified)
        decimal sizeFee = request.PackageDetails.Size switch
        {
            PackageSize.Small => 0.00m,
            PackageSize.Medium => 2.00m,
            PackageSize.Large => 5.00m,
            PackageSize.ExtraLarge => 10.00m,
            _ => 0.00m
        };

        decimal priorityFee = request.Priority switch
        {
            DeliveryPriority.Express => 5.00m,
            DeliveryPriority.Urgent => 15.00m,
            _ => 0.00m
        };

        return baseFee + distanceFee + sizeFee + priorityFee;
    }

    private DateTime CalculateEstimatedDeliveryTime(DeliveryPriority priority)
    {
        var baseTime = DateTime.UtcNow;
        return priority switch
        {
            DeliveryPriority.Urgent => baseTime.AddMinutes(30),
            DeliveryPriority.Express => baseTime.AddHours(1),
            _ => baseTime.AddHours(2)
        };
    }
}

public interface IOrderManagementService
{
    Task<ApiResponse<CreateOrderResponse>> CreateOrderAsync(Guid storeOwnerId, CreateOrderRequest request, CancellationToken cancellationToken = default);
    Task<ApiResponse<OrderDetailsResponse>> GetOrderAsync(Guid orderId, Guid userId, CancellationToken cancellationToken = default);
}

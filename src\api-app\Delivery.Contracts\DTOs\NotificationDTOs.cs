using Delivery.Contracts.Models;

namespace Delivery.Contracts.DTOs;

/// <summary>
/// Data Transfer Objects for Notification operations
/// </summary>
public class SendNotificationRequest
{
    public List<Guid> RecipientUserIds { get; set; } = new();
    public NotificationType Type { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public bool SendPush { get; set; } = true;
    public bool SendSms { get; set; } = false;
    public bool SendEmail { get; set; } = false;
}

public class NotificationResponse
{
    public Guid NotificationId { get; set; }
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<DeliveryResult> DeliveryResults { get; set; } = new();
}

public class DeliveryResult
{
    public Guid UserId { get; set; }
    public NotificationChannel Channel { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime AttemptedAt { get; set; }
}

public class RegisterDeviceTokenRequest
{
    public string DeviceToken { get; set; } = string.Empty;
    public DevicePlatform Platform { get; set; }
    public string? DeviceId { get; set; }
}

public class NotificationPreferencesRequest
{
    public bool EnablePushNotifications { get; set; } = true;
    public bool EnableSmsNotifications { get; set; } = false;
    public bool EnableEmailNotifications { get; set; } = true;
    public List<NotificationType> DisabledNotificationTypes { get; set; } = new();
}

public class NotificationHistoryResponse
{
    public List<NotificationHistoryItem> Notifications { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
}

public class NotificationHistoryItem
{
    public Guid NotificationId { get; set; }
    public NotificationType Type { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime SentAt { get; set; }
    public bool IsRead { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

public enum NotificationChannel
{
    Push = 1,
    Sms = 2,
    Email = 3
}

public enum DevicePlatform
{
    iOS = 1,
    Android = 2,
    Web = 3
}

is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = Delivery.ApiGateway
build_property.RootNamespace = Delivery.ApiGateway
build_property.ProjectDir = F:\0_____sources\0_____prcsrly\0_delievery-station\system-v1\src\api-app\Delivery.ApiGateway\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = F:\0_____sources\0_____prcsrly\0_delievery-station\system-v1\src\api-app\Delivery.ApiGateway
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

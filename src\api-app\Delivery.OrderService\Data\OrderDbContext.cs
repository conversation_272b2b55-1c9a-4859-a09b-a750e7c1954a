using Microsoft.EntityFrameworkCore;
using Delivery.Contracts.Models;

namespace Delivery.OrderService.Data;

/// <summary>
/// Entity Framework DbContext for Order Service
/// </summary>
public class OrderDbContext : DbContext
{
    public OrderDbContext(DbContextOptions<OrderDbContext> options) : base(options)
    {
    }

    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderStatusHistory> OrderStatusHistory { get; set; }
    public DbSet<User> Users { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Order entity
        modelBuilder.Entity<Order>(entity =>
        {
            entity.HasKey(e => e.OrderId);
            entity.Property(e => e.OrderId).ValueGeneratedOnAdd();
            
            entity.Property(e => e.StoreOwnerId).IsRequired();
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.DeliveryFee).HasPrecision(10, 2);
            entity.Property(e => e.CreatedAt).IsRequired();
            
            // Configure complex types as JSON
            entity.OwnsOne(e => e.PickupAddress, address =>
            {
                address.Property(a => a.Street).HasMaxLength(500);
                address.Property(a => a.City).HasMaxLength(100);
                address.Property(a => a.State).HasMaxLength(100);
                address.Property(a => a.PostalCode).HasMaxLength(20);
                address.Property(a => a.Country).HasMaxLength(100);
                address.Property(a => a.BuildingInfo).HasMaxLength(500);
            });
            
            entity.OwnsOne(e => e.DropoffAddress, address =>
            {
                address.Property(a => a.Street).HasMaxLength(500);
                address.Property(a => a.City).HasMaxLength(100);
                address.Property(a => a.State).HasMaxLength(100);
                address.Property(a => a.PostalCode).HasMaxLength(20);
                address.Property(a => a.Country).HasMaxLength(100);
                address.Property(a => a.BuildingInfo).HasMaxLength(500);
            });
            
            entity.OwnsOne(e => e.RecipientDetails, recipient =>
            {
                recipient.Property(r => r.Name).HasMaxLength(200).IsRequired();
                recipient.Property(r => r.PhoneNumber).HasMaxLength(20).IsRequired();
                recipient.Property(r => r.Email).HasMaxLength(200);
                recipient.Property(r => r.AlternatePhoneNumber).HasMaxLength(20);
            });
            
            entity.OwnsOne(e => e.PackageDetails, package =>
            {
                package.Property(p => p.Description).HasMaxLength(1000).IsRequired();
                package.Property(p => p.Weight).HasPrecision(8, 2);
                package.Property(p => p.Value).HasPrecision(10, 2);
            });
            
            entity.OwnsOne(e => e.ProofOfDelivery, proof =>
            {
                proof.Property(p => p.PhotoUrl).HasMaxLength(1000);
                proof.Property(p => p.SignatureUrl).HasMaxLength(1000);
                proof.Property(p => p.Notes).HasMaxLength(1000);
            });
            
            entity.Property(e => e.SpecialInstructions).HasMaxLength(1000);
            
            // Indexes
            entity.HasIndex(e => e.StoreOwnerId);
            entity.HasIndex(e => e.AgentId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.CreatedAt);
        });

        // Configure OrderStatusHistory entity
        modelBuilder.Entity<OrderStatusHistory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            
            entity.Property(e => e.OrderId).IsRequired();
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.Timestamp).IsRequired();
            entity.Property(e => e.Notes).HasMaxLength(1000);
            
            entity.HasIndex(e => e.OrderId);
            entity.HasIndex(e => e.Timestamp);
        });

        // Configure User entity (read-only for Order Service)
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.UserId);
            entity.Property(e => e.Email).HasMaxLength(200).IsRequired();
            entity.Property(e => e.FirstName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.LastName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.PhoneNumber).HasMaxLength(20).IsRequired();
            entity.Property(e => e.PayoutInfo).HasMaxLength(500);
            
            entity.HasIndex(e => e.Email).IsUnique();
        });
    }
}

/// <summary>
/// Entity for tracking order status changes
/// </summary>
public class OrderStatusHistory
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public OrderStatus Status { get; set; }
    public DateTime Timestamp { get; set; }
    public string? Notes { get; set; }
    public Guid? UpdatedBy { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
}

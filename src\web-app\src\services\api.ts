import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { User } from '../contexts/AuthContext'

// API Response types
interface ApiResponse<T> {
  success: boolean
  data?: T
  message: string
  errors: string[]
  timestamp: string
  traceId?: string
}

interface LoginResponse {
  accessToken: string
  refreshToken: string
  expiresAt: string
  user: User
}

interface CreateOrderRequest {
  pickupAddress: Address
  dropoffAddress: Address
  recipientDetails: RecipientDetails
  packageDetails: PackageDetails
  specialInstructions?: string
  priority: 'Standard' | 'Express' | 'Urgent'
}

interface Address {
  street: string
  city: string
  state: string
  postalCode: string
  country: string
  latitude: number
  longitude: number
  buildingInfo?: string
}

interface RecipientDetails {
  name: string
  phoneNumber: string
  email?: string
  alternatePhoneNumber?: string
}

interface PackageDetails {
  description: string
  weight: number
  size: 'Small' | 'Medium' | 'Large' | 'ExtraLarge'
  value?: number
  isFragile: boolean
  requiresSignature: boolean
}

interface CreateOrderResponse {
  orderId: string
  status: string
  deliveryFee: number
  estimatedDeliveryTime: string
  message: string
}

interface OrderDetails {
  orderId: string
  storeOwnerId: string
  agentId?: string
  status: string
  pickupAddress: Address
  dropoffAddress: Address
  recipientDetails: RecipientDetails
  packageDetails: PackageDetails
  deliveryFee: number
  createdAt: string
  estimatedDeliveryTime?: string
  specialInstructions?: string
  assignedAgent?: any
  proofOfDelivery?: any
  statusHistory: any[]
}

// Create axios instance
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api',
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // Request interceptor to add auth token
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('accessToken')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // Response interceptor to handle token refresh
  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config

      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true

        try {
          const refreshToken = localStorage.getItem('refreshToken')
          if (refreshToken) {
            const response = await axios.post('/api/auth/refresh', {
              refreshToken,
            })

            const { accessToken } = response.data.data
            localStorage.setItem('accessToken', accessToken)

            originalRequest.headers.Authorization = `Bearer ${accessToken}`
            return instance(originalRequest)
          }
        } catch (refreshError) {
          localStorage.removeItem('accessToken')
          localStorage.removeItem('refreshToken')
          window.location.href = '/login'
        }
      }

      return Promise.reject(error)
    }
  )

  return instance
}

const api = createApiInstance()

// Auth API
export const authApi = {
  login: async (email: string, password: string): Promise<LoginResponse> => {
    const response: AxiosResponse<ApiResponse<LoginResponse>> = await api.post('/auth/login', {
      email,
      password,
    })
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Login failed')
    }
    
    return response.data.data!
  },

  validateToken: async (): Promise<User> => {
    const response: AxiosResponse<ApiResponse<User>> = await api.get('/auth/me')
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Token validation failed')
    }
    
    return response.data.data!
  },

  register: async (userData: any): Promise<User> => {
    const response: AxiosResponse<ApiResponse<User>> = await api.post('/auth/register', userData)
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Registration failed')
    }
    
    return response.data.data!
  },
}

// Orders API
export const ordersApi = {
  createOrder: async (orderData: CreateOrderRequest): Promise<CreateOrderResponse> => {
    const response: AxiosResponse<ApiResponse<CreateOrderResponse>> = await api.post('/order', orderData)
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to create order')
    }
    
    return response.data.data!
  },

  getOrder: async (orderId: string): Promise<OrderDetails> => {
    const response: AxiosResponse<ApiResponse<OrderDetails>> = await api.get(`/order/${orderId}`)
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch order')
    }
    
    return response.data.data!
  },

  getOrders: async (page = 1, pageSize = 20): Promise<any> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.get('/order', {
      params: { pageNumber: page, pageSize },
    })
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch orders')
    }
    
    return response.data.data!
  },

  updateOrderStatus: async (orderId: string, status: string, notes?: string): Promise<void> => {
    const response: AxiosResponse<ApiResponse<void>> = await api.patch(`/order/${orderId}/status`, {
      status,
      notes,
    })
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to update order status')
    }
  },
}

export default api

namespace Delivery.Contracts.Models;

/// <summary>
/// Enumerations for the delivery platform
/// </summary>
public enum UserRole
{
    StoreOwner = 1,
    DeliveryAgent = 2,
    Admin = 3
}

public enum OrderStatus
{
    Pending = 1,
    AwaitingAgentAcceptance = 2,
    Accepted = 3,
    PickedUp = 4,
    InTransit = 5,
    Delivered = 6,
    Cancelled = 7,
    Failed = 8
}

public enum PackageSize
{
    Small = 1,      // Up to 1kg, fits in a bag
    Medium = 2,     // 1-5kg, small box
    Large = 3,      // 5-15kg, medium box
    ExtraLarge = 4  // 15kg+, large box or multiple items
}

public enum NotificationType
{
    OrderCreated = 1,
    OrderAssigned = 2,
    OrderAccepted = 3,
    OrderPickedUp = 4,
    OrderInTransit = 5,
    OrderDelivered = 6,
    OrderCancelled = 7,
    OrderDelayed = 8,
    AgentLocationUpdate = 9,
    PaymentProcessed = 10
}

public enum DeliveryPriority
{
    Standard = 1,
    Express = 2,
    Urgent = 3
}

public enum AgentStatus
{
    Offline = 1,
    Available = 2,
    Busy = 3,
    OnBreak = 4
}

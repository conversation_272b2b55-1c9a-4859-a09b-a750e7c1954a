using Delivery.Contracts.Models;

namespace Delivery.Contracts.DTOs;

/// <summary>
/// Data Transfer Objects for Location and Routing operations
/// </summary>
public class UpdateLocationRequest
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Heading { get; set; }
    public double? Speed { get; set; }
    public bool IsAvailable { get; set; } = true;
}

public class LocationResponse
{
    public Guid AgentId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public DateTime Timestamp { get; set; }
    public bool IsAvailable { get; set; }
    public double? Heading { get; set; }
    public double? Speed { get; set; }
}

public class FindNearbyAgentsRequest
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double RadiusKm { get; set; } = 10.0;
    public int MaxResults { get; set; } = 10;
}

public class NearbyAgentResponse
{
    public Guid AgentId { get; set; }
    public string AgentName { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double DistanceKm { get; set; }
    public int CurrentLoad { get; set; }
    public double? Rating { get; set; }
    public DateTime LastLocationUpdate { get; set; }
}

public class RouteOptimizationRequest
{
    public Guid AgentId { get; set; }
    public List<RoutePoint> Points { get; set; } = new();
}

public class RoutePoint
{
    public Guid OrderId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public RoutePointType Type { get; set; }
    public int Priority { get; set; } = 1;
}

public class OptimizedRouteResponse
{
    public List<RoutePoint> OptimizedRoute { get; set; } = new();
    public double TotalDistanceKm { get; set; }
    public int EstimatedDurationMinutes { get; set; }
    public decimal EstimatedFuelCost { get; set; }
}

public class GeofenceEvent
{
    public Guid AgentId { get; set; }
    public Guid OrderId { get; set; }
    public GeofenceEventType EventType { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public DateTime Timestamp { get; set; }
}

public enum RoutePointType
{
    Pickup = 1,
    Dropoff = 2
}

public enum GeofenceEventType
{
    EnteredPickupZone = 1,
    ExitedPickupZone = 2,
    EnteredDropoffZone = 3,
    ExitedDropoffZone = 4
}

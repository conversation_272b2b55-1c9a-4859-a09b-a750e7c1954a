#!/bin/bash

# Delivery Platform Development Stop Script
# This script stops all running services

echo "🛑 Stopping Delivery Platform Development Environment..."

# Stop background processes
if [ -f "logs/order-service.pid" ]; then
    echo "📦 Stopping Order Service..."
    kill $(cat logs/order-service.pid) 2>/dev/null
    rm logs/order-service.pid
fi

if [ -f "logs/user-service.pid" ]; then
    echo "👤 Stopping User Service..."
    kill $(cat logs/user-service.pid) 2>/dev/null
    rm logs/user-service.pid
fi

if [ -f "logs/location-service.pid" ]; then
    echo "📍 Stopping Location Service..."
    kill $(cat logs/location-service.pid) 2>/dev/null
    rm logs/location-service.pid
fi

if [ -f "logs/notification-service.pid" ]; then
    echo "🔔 Stopping Notification Service..."
    kill $(cat logs/notification-service.pid) 2>/dev/null
    rm logs/notification-service.pid
fi

if [ -f "logs/realtime-service.pid" ]; then
    echo "⚡ Stopping Realtime Service..."
    kill $(cat logs/realtime-service.pid) 2>/dev/null
    rm logs/realtime-service.pid
fi

if [ -f "logs/web-app.pid" ]; then
    echo "🌐 Stopping Web Application..."
    kill $(cat logs/web-app.pid) 2>/dev/null
    rm logs/web-app.pid
fi

# Stop Docker services
echo "🐳 Stopping infrastructure services..."
docker-compose down

echo ""
echo "✅ All services have been stopped!"
echo "📝 Logs are preserved in the 'logs' directory"

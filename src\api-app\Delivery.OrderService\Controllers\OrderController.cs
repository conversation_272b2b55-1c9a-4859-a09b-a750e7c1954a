using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Delivery.Contracts.DTOs;
using Delivery.Contracts.Common;
using Delivery.OrderService.Services;

namespace Delivery.OrderService.Controllers;

/// <summary>
/// Controller for managing delivery orders
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class OrderController : ControllerBase
{
    private readonly IOrderManagementService _orderService;
    private readonly ILogger<OrderController> _logger;

    public OrderController(
        IOrderManagementService orderService,
        ILogger<OrderController> logger)
    {
        _orderService = orderService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new delivery order
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<CreateOrderResponse>>> CreateOrder(
        [FromBody] CreateOrderRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<CreateOrderResponse>.ErrorResult("User not authenticated"));
            }

            var result = await _orderService.CreateOrderAsync(userId.Value, request, cancellationToken);
            
            if (result.Success)
            {
                return CreatedAtAction(nameof(GetOrder), new { orderId = result.Data!.OrderId }, result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating order");
            return StatusCode(500, ApiResponse<CreateOrderResponse>.ErrorResult("Internal server error"));
        }
    }

    /// <summary>
    /// Get order details by ID
    /// </summary>
    [HttpGet("{orderId:guid}")]
    public async Task<ActionResult<ApiResponse<OrderDetailsResponse>>> GetOrder(
        Guid orderId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<OrderDetailsResponse>.ErrorResult("User not authenticated"));
            }

            var result = await _orderService.GetOrderAsync(orderId, userId.Value, cancellationToken);
            
            if (result.Success)
            {
                return Ok(result);
            }

            if (result.Message.Contains("not found"))
            {
                return NotFound(result);
            }

            if (result.Message.Contains("Unauthorized"))
            {
                return Forbid();
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving order {OrderId}", orderId);
            return StatusCode(500, ApiResponse<OrderDetailsResponse>.ErrorResult("Internal server error"));
        }
    }

    /// <summary>
    /// Update order status
    /// </summary>
    [HttpPatch("{orderId:guid}/status")]
    public async Task<ActionResult<ApiResponse>> UpdateOrderStatus(
        Guid orderId,
        [FromBody] UpdateOrderStatusRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse.ErrorResult("User not authenticated"));
            }

            // TODO: Implement status update logic
            // This would involve checking permissions, updating the order status,
            // creating status history, and publishing events

            return Ok(ApiResponse.SuccessResult("Order status updated successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating order status for order {OrderId}", orderId);
            return StatusCode(500, ApiResponse.ErrorResult("Internal server error"));
        }
    }

    /// <summary>
    /// Get orders for the current user
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<OrderListResponse>>> GetOrders(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<OrderListResponse>.ErrorResult("User not authenticated"));
            }

            // TODO: Implement get orders logic
            // This would involve querying orders based on user role and permissions

            var response = new OrderListResponse
            {
                Orders = new List<OrderSummary>(),
                TotalCount = 0,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            return Ok(ApiResponse<OrderListResponse>.SuccessResult(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving orders for user");
            return StatusCode(500, ApiResponse<OrderListResponse>.ErrorResult("Internal server error"));
        }
    }

    /// <summary>
    /// Submit proof of delivery
    /// </summary>
    [HttpPost("{orderId:guid}/proof-of-delivery")]
    public async Task<ActionResult<ApiResponse>> SubmitProofOfDelivery(
        Guid orderId,
        [FromBody] ProofOfDeliveryRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse.ErrorResult("User not authenticated"));
            }

            // TODO: Implement proof of delivery submission logic
            // This would involve validating the agent, updating the order,
            // storing the proof files, and publishing events

            return Ok(ApiResponse.SuccessResult("Proof of delivery submitted successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting proof of delivery for order {OrderId}", orderId);
            return StatusCode(500, ApiResponse.ErrorResult("Internal server error"));
        }
    }

    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        return null;
    }
}

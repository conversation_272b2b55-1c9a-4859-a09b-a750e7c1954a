import React from 'react'
import { NavigationContainer } from '@react-navigation/native'
import { createStackNavigator } from '@react-navigation/stack'
import { QueryClient, QueryClientProvider } from 'react-query'
import { AuthProvider } from './src/contexts/AuthContext'
import { LocationProvider } from './src/contexts/LocationContext'
import { NotificationProvider } from './src/contexts/NotificationContext'
import AuthNavigator from './src/navigation/AuthNavigator'
import MainNavigator from './src/navigation/MainNavigator'
import { useAuth } from './src/contexts/AuthContext'
import LoadingScreen from './src/screens/LoadingScreen'

const Stack = createStackNavigator()

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
})

const AppContent: React.FC = () => {
  const { user, loading } = useAuth()

  if (loading) {
    return <LoadingScreen />
  }

  return (
    <NavigationContainer>
      {user ? <MainNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  )
}

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <LocationProvider>
          <NotificationProvider>
            <AppContent />
          </NotificationProvider>
        </LocationProvider>
      </AuthProvider>
    </QueryClientProvider>
  )
}

export default App

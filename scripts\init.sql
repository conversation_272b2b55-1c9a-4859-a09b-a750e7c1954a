-- Delivery Platform Database Initialization Script

-- Create databases for each microservice
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Orders;
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Users;
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Location;
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Notifications;
CREATE DATABASE IF NOT EXISTS DeliveryPlatform_Realtime;

-- Use the main database
USE DeliveryPlatform_Orders;

-- Create sample data for development
-- Note: In production, this would be handled by Entity Framework migrations

-- Sample Users (Store Owners and Delivery Agents)
INSERT INTO Users (UserId, Email, PasswordHash, Role, FirstName, LastName, PhoneNumber, CreatedAt, UpdatedAt, IsActive) VALUES
('11111111-1111-1111-1111-111111111111', '<EMAIL>', '$2a$11$hash_for_password123', 1, 'John', 'Store', '+1234567890', NOW(), NOW(), 1),
('22222222-2222-2222-2222-222222222222', '<EMAIL>', '$2a$11$hash_for_password123', 2, 'Jane', 'Agent', '+1234567891', NOW(), NOW(), 1),
('33333333-3333-3333-3333-333333333333', '<EMAIL>', '$2a$11$hash_for_password123', 3, 'Admin', 'User', '+1234567892', NOW(), NOW(), 1);

-- Sample Orders for demonstration
INSERT INTO Orders (OrderId, StoreOwnerId, Status, DeliveryFee, CreatedAt, EstimatedDeliveryTime) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 1, 15.50, NOW(), DATE_ADD(NOW(), INTERVAL 2 HOUR)),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '11111111-1111-1111-1111-111111111111', 2, 12.75, DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_ADD(NOW(), INTERVAL 1 HOUR)),
('cccccccc-cccc-cccc-cccc-cccccccccccc', '11111111-1111-1111-1111-111111111111', 6, 18.25, DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR));

-- Create indexes for better performance
CREATE INDEX idx_orders_store_owner ON Orders(StoreOwnerId);
CREATE INDEX idx_orders_agent ON Orders(AgentId);
CREATE INDEX idx_orders_status ON Orders(Status);
CREATE INDEX idx_orders_created_at ON Orders(CreatedAt);

-- Create spatial indexes for location-based queries
-- Note: These would be created by the Location Service

COMMIT;

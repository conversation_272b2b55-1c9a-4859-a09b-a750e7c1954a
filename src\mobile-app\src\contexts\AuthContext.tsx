import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { authApi } from '../services/api'

export interface User {
  userId: string
  email: string
  firstName: string
  lastName: string
  role: 'StoreOwner' | 'DeliveryAgent' | 'Admin'
  phoneNumber: string
  isActive: boolean
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  updateUser: (userData: Partial<User>) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = await AsyncStorage.getItem('accessToken')
        if (token) {
          // Validate token and get user info
          const userData = await authApi.validateToken()
          setUser(userData)
        }
      } catch (error) {
        console.error('Auth initialization failed:', error)
        await AsyncStorage.multiRemove(['accessToken', 'refreshToken'])
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const login = async (email: string, password: string) => {
    try {
      const response = await authApi.login(email, password)
      
      await AsyncStorage.setItem('accessToken', response.accessToken)
      await AsyncStorage.setItem('refreshToken', response.refreshToken)
      
      setUser(response.user)
    } catch (error) {
      throw error
    }
  }

  const logout = async () => {
    await AsyncStorage.multiRemove(['accessToken', 'refreshToken'])
    setUser(null)
  }

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData })
    }
  }

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    updateUser,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
